using System;
using System.Numerics;
using ImGuiNET;

namespace PvPLinePlugin.Graphics;

/// <summary>
/// Enhanced UI controls for advanced graphics configuration
/// </summary>
public static class EnhancedUI
{
    private static PreviewRenderer? _previewRenderer;

    /// <summary>
    /// Draw enhanced indicator type selector with preview
    /// </summary>
    public static bool DrawIndicatorTypeSelector(ref EnhancedIndicatorType currentType, EnhancedConfiguration config)
    {
        var changed = false;
        var indicatorNames = Enum.GetNames<EnhancedIndicatorType>();
        var currentIndex = (int)currentType;

        ImGui.Text("Enhanced Indicator Type:");
        
        if (ImGui.Combo("##EnhancedIndicatorType", ref currentIndex, indicatorNames, indicatorNames.Length))
        {
            currentType = (EnhancedIndicatorType)currentIndex;
            changed = true;
        }

        // Show description based on selected type
        ImGui.TextWrapped(GetIndicatorDescription(currentType));

        // Show preview if enhanced graphics are enabled
        if (config.UseEnhancedGraphics)
        {
            ImGui.Spacing();
            DrawLivePreview(config);
        }

        return changed;
    }

    /// <summary>
    /// Draw animation controls
    /// </summary>
    public static bool DrawAnimationControls(EnhancedConfiguration config)
    {
        var changed = false;

        ImGui.Text("Animation Settings:");
        ImGui.Separator();

        // Animation Type
        var animationNames = Enum.GetNames<AnimationType>();
        var currentAnimation = (int)config.AnimationType;
        if (ImGui.Combo("Animation Type", ref currentAnimation, animationNames, animationNames.Length))
        {
            config.AnimationType = (AnimationType)currentAnimation;
            changed = true;
        }

        if (config.AnimationType != AnimationType.None)
        {
            // Animation Speed
            var speed = config.AnimationSpeed;
            if (ImGui.SliderFloat("Animation Speed", ref speed, 0.1f, 5.0f))
            {
                config.AnimationSpeed = speed;
                changed = true;
            }

            // Easing Type
            var easingNames = Enum.GetNames<EasingType>();
            var currentEasing = (int)config.EasingType;
            if (ImGui.Combo("Easing Type", ref currentEasing, easingNames, easingNames.Length))
            {
                config.EasingType = (EasingType)currentEasing;
                changed = true;
            }

            // Animation-specific controls
            switch (config.AnimationType)
            {
                case AnimationType.Pulse:
                    var amplitude = config.PulseAmplitude;
                    if (ImGui.SliderFloat("Pulse Amplitude", ref amplitude, 0.1f, 1.0f))
                    {
                        config.PulseAmplitude = amplitude;
                        changed = true;
                    }
                    break;

                case AnimationType.Rotate:
                    var rotSpeed = config.RotationSpeed;
                    if (ImGui.SliderFloat("Rotation Speed", ref rotSpeed, 0.1f, 3.0f))
                    {
                        config.RotationSpeed = rotSpeed;
                        changed = true;
                    }
                    break;

                case AnimationType.Wave:
                    var waveAmp = config.WaveAmplitude;
                    if (ImGui.SliderFloat("Wave Amplitude", ref waveAmp, 1.0f, 20.0f))
                    {
                        config.WaveAmplitude = waveAmp;
                        changed = true;
                    }
                    break;

                case AnimationType.Fade:
                    var fadeAmount = config.FadeAmount;
                    if (ImGui.SliderFloat("Fade Amount", ref fadeAmount, 0.1f, 1.0f))
                    {
                        config.FadeAmount = fadeAmount;
                        changed = true;
                    }
                    break;
            }
        }

        return changed;
    }

    /// <summary>
    /// Draw visual effects controls
    /// </summary>
    public static bool DrawEffectsControls(EnhancedConfiguration config)
    {
        var changed = false;

        ImGui.Text("Visual Effects:");
        ImGui.Separator();

        // Glow Effects
        var enableGlow = config.EnableGlowEffects;
        if (ImGui.Checkbox("Enable Glow Effects", ref enableGlow))
        {
            config.EnableGlowEffects = enableGlow;
            changed = true;
        }

        if (config.EnableGlowEffects)
        {
            ImGui.Indent();
            var glowColor = config.GlowColor;
            if (ImGui.ColorEdit4("Glow Color", ref glowColor))
            {
                config.GlowColor = glowColor;
                changed = true;
            }

            var glowIntensity = config.GlowIntensity;
            if (ImGui.SliderFloat("Glow Intensity", ref glowIntensity, 0.1f, 2.0f))
            {
                config.GlowIntensity = glowIntensity;
                changed = true;
            }
            ImGui.Unindent();
        }

        // Particle Effects
        var enableParticles = config.EnableParticleEffects;
        if (ImGui.Checkbox("Enable Particle Effects", ref enableParticles))
        {
            config.EnableParticleEffects = enableParticles;
            changed = true;
        }

        if (config.EnableParticleEffects)
        {
            ImGui.Indent();
            var particleCount = config.ParticleCount;
            if (ImGui.SliderInt("Particle Count", ref particleCount, 4, 32))
            {
                config.ParticleCount = particleCount;
                changed = true;
            }

            var particleSize = config.ParticleSize;
            if (ImGui.SliderFloat("Particle Size", ref particleSize, 1.0f, 10.0f))
            {
                config.ParticleSize = particleSize;
                changed = true;
            }

            var particleRadius = config.ParticleRadius;
            if (ImGui.SliderFloat("Particle Radius", ref particleRadius, 10.0f, 50.0f))
            {
                config.ParticleRadius = particleRadius;
                changed = true;
            }
            ImGui.Unindent();
        }

        // Gradient Effects
        var enableGradients = config.EnableGradientFills;
        if (ImGui.Checkbox("Enable Gradient Fills", ref enableGradients))
        {
            config.EnableGradientFills = enableGradients;
            changed = true;
        }

        if (config.EnableGradientFills)
        {
            ImGui.Indent();
            var gradientNames = Enum.GetNames<GradientDirection>();
            var currentGradient = (int)config.GradientDirection;
            if (ImGui.Combo("Gradient Direction", ref currentGradient, gradientNames, gradientNames.Length))
            {
                config.GradientDirection = (GradientDirection)currentGradient;
                changed = true;
            }
            ImGui.Unindent();
        }

        // Trail Effects
        var enableTrails = config.EnableTrailEffects;
        if (ImGui.Checkbox("Enable Trail Effects", ref enableTrails))
        {
            config.EnableTrailEffects = enableTrails;
            changed = true;
        }

        return changed;
    }

    /// <summary>
    /// Draw zone and activation controls
    /// </summary>
    public static bool DrawZoneControls(EnhancedConfiguration config)
    {
        var changed = false;

        ImGui.Text("Activation Settings:");
        ImGui.Separator();

        var everywhereMode = config.EnhancedGraphicsEverywhereMode;
        if (ImGui.Checkbox("Enhanced Graphics Work Everywhere", ref everywhereMode))
        {
            config.EnhancedGraphicsEverywhereMode = everywhereMode;
            changed = true;
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("When enabled, enhanced graphics work in all zones, not just PvP.\nThis allows you to see the enhanced visual effects everywhere for testing and customization.");
        }

        if (!config.EnhancedGraphicsEverywhereMode)
        {
            ImGui.Indent();
            var respectPvP = config.RespectOriginalPvPSetting;
            if (ImGui.Checkbox("Respect Original PvP Zone Setting", ref respectPvP))
            {
                config.RespectOriginalPvPSetting = respectPvP;
                changed = true;
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("When enabled, enhanced graphics will only work in PvP zones,\nrespecting the original plugin's 'Only Show in PvP Zones' setting.");
            }
            ImGui.Unindent();
        }

        ImGui.Spacing();
        ImGui.TextWrapped("Note: The original plugin's PvP zone restriction still applies to classic rendering mode. Enhanced graphics can work independently of this setting.");

        return changed;
    }

    /// <summary>
    /// Draw preset selector
    /// </summary>
    public static bool DrawPresetSelector(EnhancedConfiguration config)
    {
        var changed = false;

        ImGui.Text("Visual Presets:");
        ImGui.Separator();

        var presets = Enum.GetNames<VisualPreset>();
        
        ImGui.Columns(3, "PresetColumns", false);
        
        foreach (var preset in presets)
        {
            if (ImGui.Button(preset, new Vector2(-1, 30)))
            {
                var presetEnum = Enum.Parse<VisualPreset>(preset);
                config.ApplyPreset(presetEnum);
                changed = true;
            }
            
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip(GetPresetDescription(preset));
            }
            
            ImGui.NextColumn();
        }
        
        ImGui.Columns(1);

        if (changed)
        {
            ImGui.Text($"Current Preset: {config.CurrentPreset}");
        }

        return changed;
    }

    /// <summary>
    /// Draw shape selector
    /// </summary>
    public static bool DrawShapeSelector(EnhancedConfiguration config)
    {
        var changed = false;

        ImGui.Text("Shape Settings:");
        ImGui.Separator();

        var shapeNames = Enum.GetNames<IndicatorShape>();
        var currentShape = (int)config.IndicatorShape;
        if (ImGui.Combo("Indicator Shape", ref currentShape, shapeNames, shapeNames.Length))
        {
            config.IndicatorShape = (IndicatorShape)currentShape;
            changed = true;
        }

        var shapeSize = config.ShapeSize;
        if (ImGui.SliderFloat("Shape Size", ref shapeSize, 5.0f, 50.0f))
        {
            config.ShapeSize = shapeSize;
            changed = true;
        }

        var filled = config.FilledShapes;
        if (ImGui.Checkbox("Filled Shapes", ref filled))
        {
            config.FilledShapes = filled;
            changed = true;
        }

        return changed;
    }

    /// <summary>
    /// Draw live preview with real-time updates
    /// </summary>
    public static void DrawLivePreview(EnhancedConfiguration config)
    {
        // Initialize preview renderer if needed
        _previewRenderer ??= new PreviewRenderer();

        ImGui.Separator();
        ImGui.Text("Live Preview:");
        ImGui.TextWrapped("See your changes in real-time below:");

        // Render animated preview
        _previewRenderer.RenderAnimatedPreview(config);

        ImGui.Spacing();

        // Preview controls
        if (ImGui.CollapsingHeader("Preview Controls"))
        {
            DrawPreviewControls(config);
        }
    }

    /// <summary>
    /// Draw preview control options
    /// </summary>
    private static void DrawPreviewControls(EnhancedConfiguration config)
    {
        ImGui.Text("Preview Options:");

        if (ImGui.Button("Reset to Center"))
        {
            // Reset preview camera/position
        }

        ImGui.SameLine();
        if (ImGui.Button("Pause Animation"))
        {
            // Toggle animation pause
        }

        ImGui.SameLine();
        if (ImGui.Button("Full Screen Preview"))
        {
            // Open full screen preview window
            ImGui.OpenPopup("FullScreenPreview");
        }

        // Full screen preview modal
        var isOpen = true;
        if (ImGui.BeginPopupModal("FullScreenPreview", ref isOpen, ImGuiWindowFlags.AlwaysAutoResize))
        {
            ImGui.Text("Full Screen Preview");
            ImGui.Separator();

            // Larger preview
            _previewRenderer?.RenderAnimatedPreview(config);

            ImGui.Spacing();
            if (ImGui.Button("Close"))
            {
                ImGui.CloseCurrentPopup();
            }

            ImGui.EndPopup();
        }
    }

    /// <summary>
    /// Draw a simple preview of the indicator (fallback method)
    /// </summary>
    private static void DrawIndicatorPreview(EnhancedIndicatorType type, EnhancedConfiguration config)
    {
        var drawList = ImGui.GetWindowDrawList();
        var pos = ImGui.GetCursorScreenPos();
        var size = new Vector2(200, 100);
        
        // Background
        drawList.AddRectFilled(pos, pos + size, ImGui.ColorConvertFloat4ToU32(new Vector4(0.1f, 0.1f, 0.1f, 1.0f)));
        drawList.AddRect(pos, pos + size, ImGui.ColorConvertFloat4ToU32(new Vector4(0.3f, 0.3f, 0.3f, 1.0f)));

        var center = pos + size * 0.5f;
        var color = ImGui.ColorConvertFloat4ToU32(config.PrimaryColor);

        // Draw preview based on type
        switch (type)
        {
            case EnhancedIndicatorType.Lines:
                drawList.AddLine(center - new Vector2(50, 0), center + new Vector2(50, 0), color, config.LineThickness);
                break;
            case EnhancedIndicatorType.AnimatedOutlines:
                var radius = 20f + AnimationSystem.GetPulseValue(2f, 5f);
                drawList.AddCircle(center, radius, color, 16, config.LineThickness);
                break;
            case EnhancedIndicatorType.CustomShapes:
                DrawShapePreview(drawList, center, config.IndicatorShape, 20f, color, config.LineThickness);
                break;
            default:
                drawList.AddCircle(center, 20f, color, 16, config.LineThickness);
                break;
        }

        ImGui.Dummy(size);
    }

    private static void DrawShapePreview(ImDrawListPtr drawList, Vector2 center, IndicatorShape shape, float size, uint color, float thickness)
    {
        switch (shape)
        {
            case IndicatorShape.Circle:
                drawList.AddCircle(center, size, color, 16, thickness);
                break;
            case IndicatorShape.Square:
                drawList.AddRect(center - Vector2.One * size, center + Vector2.One * size, color, 0f, ImDrawFlags.None, thickness);
                break;
            case IndicatorShape.Triangle:
                var p1 = center + new Vector2(0, -size);
                var p2 = center + new Vector2(-size * 0.866f, size * 0.5f);
                var p3 = center + new Vector2(size * 0.866f, size * 0.5f);
                drawList.AddTriangle(p1, p2, p3, color, thickness);
                break;
            case IndicatorShape.Diamond:
                var top = center + new Vector2(0, -size);
                var right = center + new Vector2(size, 0);
                var bottom = center + new Vector2(0, size);
                var left = center + new Vector2(-size, 0);
                drawList.AddQuad(top, right, bottom, left, color, thickness);
                break;
        }
    }

    private static string GetIndicatorDescription(EnhancedIndicatorType type)
    {
        return type switch
        {
            EnhancedIndicatorType.Lines => "Classic lines connecting you to enemies",
            EnhancedIndicatorType.AnimatedOutlines => "Pulsing, rotating outlines around enemies",
            EnhancedIndicatorType.GradientFills => "Smooth color gradients with customizable patterns",
            EnhancedIndicatorType.ParticleEffects => "Animated particles surrounding enemy positions",
            EnhancedIndicatorType.CustomShapes => "Geometric shapes with various forms and animations",
            EnhancedIndicatorType.GlowEffects => "Soft glowing halos with adjustable intensity",
            EnhancedIndicatorType.NeonStyle => "Bright, vibrant neon-like effects",
            EnhancedIndicatorType.TacticalOverlay => "Military-style tactical display elements",
            EnhancedIndicatorType.HolographicStyle => "Futuristic holographic-style indicators",
            EnhancedIndicatorType.MinimalistDots => "Clean, simple dot indicators for minimal distraction",
            _ => "Advanced indicator with customizable visual effects"
        };
    }

    private static string GetPresetDescription(string preset)
    {
        return preset switch
        {
            "Minimal" => "Clean, simple indicators with minimal visual effects",
            "Standard" => "Balanced visibility with subtle animations",
            "Enhanced" => "Rich visual effects with glow and animations",
            "Neon" => "Bright, vibrant neon-style effects",
            "Tactical" => "Military-style tactical display",
            "Holographic" => "Futuristic holographic appearance",
            _ => "Custom visual configuration"
        };
    }

    /// <summary>
    /// Cleanup resources used by the enhanced UI
    /// </summary>
    public static void Cleanup()
    {
        _previewRenderer?.Dispose();
        _previewRenderer = null;
    }
}
