using System;
using System.Numerics;

namespace PvPLinePlugin.Graphics;

/// <summary>
/// Animation system for smooth visual effects
/// </summary>
public static class AnimationSystem
{
    /// <summary>
    /// Get current time in seconds for animations
    /// </summary>
    public static float CurrentTime => (float)(DateTime.UtcNow.Ticks / 10000000.0);

    /// <summary>
    /// Apply easing function to a value between 0 and 1
    /// </summary>
    public static float ApplyEasing(float t, EasingType easing)
    {
        t = Math.Clamp(t, 0f, 1f);
        
        return easing switch
        {
            EasingType.Linear => t,
            EasingType.EaseIn => t * t,
            EasingType.EaseOut => 1f - (1f - t) * (1f - t),
            EasingType.EaseInOut => t < 0.5f ? 2f * t * t : 1f - 2f * (1f - t) * (1f - t),
            EasingType.Bounce => BounceEase(t),
            EasingType.Elastic => ElasticEase(t),
            EasingType.Back => BackEase(t),
            EasingType.Sine => (float)(1f - Math.Cos(t * Math.PI / 2f)),
            EasingType.Quad => t * t,
            EasingType.Cubic => t * t * t,
            EasingType.Quart => t * t * t * t,
            EasingType.Quint => t * t * t * t * t,
            EasingType.Expo => t == 0f ? 0f : (float)Math.Pow(2f, 10f * (t - 1f)),
            EasingType.Circ => 1f - (float)Math.Sqrt(1f - t * t),
            _ => t
        };
    }

    /// <summary>
    /// Get animated value for pulse effect
    /// </summary>
    public static float GetPulseValue(float speed = 1f, float amplitude = 1f, EasingType easing = EasingType.Sine)
    {
        var time = CurrentTime * speed;
        var t = (float)(Math.Sin(time) * 0.5f + 0.5f);
        return ApplyEasing(t, easing) * amplitude;
    }

    /// <summary>
    /// Get animated rotation value
    /// </summary>
    public static float GetRotationValue(float speed = 1f)
    {
        return (CurrentTime * speed) % (2f * (float)Math.PI);
    }

    /// <summary>
    /// Get animated wave value
    /// </summary>
    public static float GetWaveValue(float frequency = 1f, float amplitude = 1f, float phase = 0f)
    {
        return (float)(Math.Sin(CurrentTime * frequency + phase) * amplitude);
    }

    /// <summary>
    /// Get animated position offset for flowing effects
    /// </summary>
    public static Vector2 GetFlowOffset(float speed = 1f, Vector2 direction = default)
    {
        if (direction == default)
            direction = Vector2.UnitX;
        
        var time = CurrentTime * speed;
        return direction * (time % 1f);
    }

    /// <summary>
    /// Get animated scale for breathing/pulsing effects
    /// </summary>
    public static float GetBreathingScale(float baseScale = 1f, float amplitude = 0.2f, float speed = 1f)
    {
        var pulse = GetPulseValue(speed, amplitude);
        return baseScale + pulse;
    }

    /// <summary>
    /// Get animated opacity for fading effects
    /// </summary>
    public static float GetFadeOpacity(float baseOpacity = 1f, float fadeAmount = 0.3f, float speed = 1f)
    {
        var pulse = GetPulseValue(speed, fadeAmount);
        return Math.Clamp(baseOpacity - pulse, 0f, 1f);
    }

    /// <summary>
    /// Get animated color for color cycling effects
    /// </summary>
    public static Vector4 GetCyclingColor(Vector4 baseColor, float speed = 1f, float intensity = 0.2f)
    {
        var time = CurrentTime * speed;
        var hueShift = (float)(Math.Sin(time) * intensity);
        
        // Simple hue shifting (this is a basic implementation)
        return new Vector4(
            Math.Clamp(baseColor.X + hueShift, 0f, 1f),
            Math.Clamp(baseColor.Y + hueShift * 0.5f, 0f, 1f),
            Math.Clamp(baseColor.Z + hueShift * 0.3f, 0f, 1f),
            baseColor.W
        );
    }

    /// <summary>
    /// Interpolate between two values
    /// </summary>
    public static float Lerp(float a, float b, float t)
    {
        return a + (b - a) * Math.Clamp(t, 0f, 1f);
    }

    /// <summary>
    /// Interpolate between two vectors
    /// </summary>
    public static Vector2 Lerp(Vector2 a, Vector2 b, float t)
    {
        return Vector2.Lerp(a, b, Math.Clamp(t, 0f, 1f));
    }

    /// <summary>
    /// Interpolate between two colors
    /// </summary>
    public static Vector4 Lerp(Vector4 a, Vector4 b, float t)
    {
        return Vector4.Lerp(a, b, Math.Clamp(t, 0f, 1f));
    }

    // Private helper methods for complex easing functions
    private static float BounceEase(float t)
    {
        if (t < 1f / 2.75f)
            return 7.5625f * t * t;
        else if (t < 2f / 2.75f)
            return 7.5625f * (t -= 1.5f / 2.75f) * t + 0.75f;
        else if (t < 2.5f / 2.75f)
            return 7.5625f * (t -= 2.25f / 2.75f) * t + 0.9375f;
        else
            return 7.5625f * (t -= 2.625f / 2.75f) * t + 0.984375f;
    }

    private static float ElasticEase(float t)
    {
        if (t == 0f) return 0f;
        if (t == 1f) return 1f;
        
        var p = 0.3f;
        var s = p / 4f;
        return (float)(Math.Pow(2f, -10f * t) * Math.Sin((t - s) * (2f * Math.PI) / p) + 1f);
    }

    private static float BackEase(float t)
    {
        var s = 1.70158f;
        return t * t * ((s + 1f) * t - s);
    }
}
