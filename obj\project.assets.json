{"version": 3, "targets": {"net9.0-windows7.0": {"DalamudPackager/11.0.0": {"type": "package", "build": {"build/DalamudPackager.props": {}, "build/DalamudPackager.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/DalamudPackager.props": {}}}, "SkiaSharp/3.119.0": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "3.119.0", "SkiaSharp.NativeAssets.macOS": "3.119.0"}, "compile": {"ref/net8.0/SkiaSharp.dll": {}}, "runtime": {"lib/net8.0/SkiaSharp.dll": {"related": ".pdb"}}}, "SkiaSharp.NativeAssets.macOS/3.119.0": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.Win32/3.119.0": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.Numerics.Vectors/4.6.1": {"type": "package", "compile": {"lib/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}}}, "libraries": {"DalamudPackager/11.0.0": {"sha512": "bjT7XUlhIJSmsE/O76b7weUX+evvGQctbQB8aKXt94o+oPWxHpCepxAGMs7Thow3AzCyqWs7cOpp9/2wcgRRQA==", "type": "package", "path": "dalamudpackager/11.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/DalamudPackager.props", "build/DalamudPackager.targets", "buildMultiTargeting/DalamudPackager.props", "dalamudpackager.11.0.0.nupkg.sha512", "dalamudpackager.nuspec", "tasks/net48/DalamudPackager.dll", "tasks/net48/Newtonsoft.Json.dll", "tasks/net48/YamlDotNet.dll", "tasks/netstandard2.1/DalamudPackager.dll", "tasks/netstandard2.1/Newtonsoft.Json.dll", "tasks/netstandard2.1/YamlDotNet.dll"]}, "SkiaSharp/3.119.0": {"sha512": "gR9yVoOta2Mc1Rxt15LD65AckfHMfwjIs/3kkD59C9bT2nYYISsE6uz3t4aMPNHA6CgsIL0Ssn+jE5OVilZ1yw==", "type": "package", "path": "skiasharp/3.119.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "icon.png", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net8.0-android34.0/SkiaSharp.dll", "lib/net8.0-android34.0/SkiaSharp.pdb", "lib/net8.0-android34.0/SkiaSharp.xml", "lib/net8.0-ios17.0/SkiaSharp.dll", "lib/net8.0-ios17.0/SkiaSharp.pdb", "lib/net8.0-maccatalyst17.0/SkiaSharp.dll", "lib/net8.0-maccatalyst17.0/SkiaSharp.pdb", "lib/net8.0-macos14.0/SkiaSharp.dll", "lib/net8.0-macos14.0/SkiaSharp.pdb", "lib/net8.0-tizen7.0/SkiaSharp.dll", "lib/net8.0-tizen7.0/SkiaSharp.pdb", "lib/net8.0-tvos17.0/SkiaSharp.dll", "lib/net8.0-tvos17.0/SkiaSharp.pdb", "lib/net8.0-windows10.0.19041/SkiaSharp.dll", "lib/net8.0-windows10.0.19041/SkiaSharp.pdb", "lib/net8.0/SkiaSharp.dll", "lib/net8.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "ref/net462/SkiaSharp.dll", "ref/net6.0/SkiaSharp.dll", "ref/net8.0-android34.0/SkiaSharp.dll", "ref/net8.0-ios17.0/SkiaSharp.dll", "ref/net8.0-maccatalyst17.0/SkiaSharp.dll", "ref/net8.0-macos14.0/SkiaSharp.dll", "ref/net8.0-tizen7.0/SkiaSharp.dll", "ref/net8.0-tvos17.0/SkiaSharp.dll", "ref/net8.0-windows10.0.19041/SkiaSharp.dll", "ref/net8.0/SkiaSharp.dll", "ref/netstandard2.0/SkiaSharp.dll", "ref/netstandard2.1/SkiaSharp.dll", "skiasharp.3.119.0.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.NativeAssets.macOS/3.119.0": {"sha512": "YE1vNn0Nyw2PWtv7hw1PYkKJO0itFiQp9vSqGppZUKzQJqwp28a2jgdCMPfYtOiR8KCnDgZqQoynqJRRaE2ZVg==", "type": "package", "path": "skiasharp.nativeassets.macos/3.119.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net8.0-macos14.0/SkiaSharp.NativeAssets.macOS.targets", "icon.png", "lib/net462/_._", "lib/net6.0/_._", "lib/net8.0-macos14.0/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.3.119.0.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.Win32/3.119.0": {"sha512": "IwC9yx36lOdXVT2DjgmWHl1qkVspfj8ctd4+li8CNnvqdfaTolXCOh6TLznURcPAvzatx9K/tLOB7zT6T8EA9w==", "type": "package", "path": "skiasharp.nativeassets.win32/3.119.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "icon.png", "lib/net462/_._", "lib/net6.0-windows10.0.19041/_._", "lib/net6.0/_._", "lib/net8.0-windows10.0.19041/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.3.119.0.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "System.Numerics.Vectors/4.6.1": {"sha512": "sQxefTnhagrhoq2ReR0D/6K0zJcr9Hrd6kikeXsA1I8kOCboTavcUC4r7TSfpKFeE163uMuxZcyfO1mGO3EN8Q==", "type": "package", "path": "system.numerics.vectors/4.6.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Numerics.Vectors.targets", "buildTransitive/net462/_._", "lib/net462/System.Numerics.Vectors.dll", "lib/net462/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/netstandard2.1/_._", "system.numerics.vectors.4.6.1.nupkg.sha512", "system.numerics.vectors.nuspec"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> >= 2.1.14", "SkiaSharp >= 3.119.0", "System.Numerics.Vectors >= 4.6.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\CCDraw\\PvPLinePlugin\\PvPLinePlugin.csproj", "projectName": "PvPLinePlugin", "projectPath": "C:\\Users\\<USER>\\Desktop\\CCDraw\\PvPLinePlugin\\PvPLinePlugin.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\CCDraw\\PvPLinePlugin\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreLockProperties": {"restorePackagesWithLockFile": "true"}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"DalamudPackager": {"target": "Package", "version": "[2.1.14, )"}, "SkiaSharp": {"target": "Package", "version": "[3.119.0, )"}, "System.Numerics.Vectors": {"target": "Package", "version": "[4.6.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1601", "level": "Warning", "warningLevel": 1, "message": "Dependency specified was <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (>= 2.1.14) but ended up with <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 11.0.0.", "libraryId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetGraphs": ["net9.0-windows7.0"]}]}