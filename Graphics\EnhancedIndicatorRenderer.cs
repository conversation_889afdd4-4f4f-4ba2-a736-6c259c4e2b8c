using System;
using System.Collections.Generic;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;
using ImGuiNET;

namespace PvPLinePlugin.Graphics;

/// <summary>
/// Main renderer for enhanced indicators that integrates with the existing plugin
/// </summary>
public class EnhancedIndicatorRenderer : IDisposable
{
    private readonly EnhancedGraphicsRenderer _graphicsRenderer;
    private readonly EnhancedConfiguration _config;
    private readonly Dictionary<ulong, PlayerIndicatorState> _playerStates;
    private bool _disposed = false;

    public EnhancedIndicatorRenderer(EnhancedConfiguration config)
    {
        _config = config;
        _graphicsRenderer = new EnhancedGraphicsRenderer();
        _playerStates = new Dictionary<ulong, PlayerIndicatorState>();
    }

    /// <summary>
    /// Initialize the renderer
    /// </summary>
    public bool Initialize()
    {
        var io = ImGui.GetIO();
        var displaySize = io.DisplaySize;
        return _graphicsRenderer.Initialize((int)displaySize.X, (int)displaySize.Y);
    }

    /// <summary>
    /// Render indicators for a list of players
    /// </summary>
    public void RenderIndicators(IEnumerable<IPlayerCharacter> players, Vector3 localPlayerPosition)
    {
        if (!_config.UseEnhancedGraphics) return;

        // Enhanced graphics can work everywhere if EnhancedGraphicsEverywhereMode is enabled
        if (!ShouldRenderEnhancedGraphics()) return;

        _graphicsRenderer.BeginFrame();

        foreach (var player in players)
        {
            if (player?.GameObjectId == null) continue;

            // Update or create player state
            if (!_playerStates.TryGetValue(player.GameObjectId, out var state))
            {
                state = new PlayerIndicatorState(player.GameObjectId);
                _playerStates[player.GameObjectId] = state;
            }

            // Update state
            state.Update(player, localPlayerPosition);

            // Render based on indicator type
            RenderPlayerIndicator(player, state);
        }

        // Clean up old player states
        CleanupOldStates();

        var image = _graphicsRenderer.EndFrame();
        // TODO: Display the rendered image in ImGui
    }

    /// <summary>
    /// Render indicator for a specific player
    /// </summary>
    private void RenderPlayerIndicator(IPlayerCharacter player, PlayerIndicatorState state)
    {
        var screenPos = WorldToScreen(player.Position);
        if (!screenPos.HasValue) return;

        var pos2D = new Vector2(screenPos.Value.X, screenPos.Value.Y);

        switch (_config.EnhancedIndicatorType)
        {
            case EnhancedIndicatorType.Lines:
                RenderEnhancedLine(state, pos2D);
                break;

            case EnhancedIndicatorType.AnimatedOutlines:
                RenderAnimatedOutline(state, pos2D);
                break;

            case EnhancedIndicatorType.GradientFills:
                RenderGradientFill(state, pos2D);
                break;

            case EnhancedIndicatorType.ParticleEffects:
                RenderParticleEffect(state, pos2D);
                break;

            case EnhancedIndicatorType.CustomShapes:
                RenderCustomShape(state, pos2D);
                break;

            case EnhancedIndicatorType.GlowEffects:
                RenderGlowEffect(state, pos2D);
                break;

            case EnhancedIndicatorType.NeonStyle:
                RenderNeonStyle(state, pos2D);
                break;

            case EnhancedIndicatorType.TacticalOverlay:
                RenderTacticalOverlay(state, pos2D);
                break;

            case EnhancedIndicatorType.HolographicStyle:
                RenderHolographicStyle(state, pos2D);
                break;

            case EnhancedIndicatorType.MinimalistDots:
                RenderMinimalistDot(state, pos2D);
                break;

            default:
                RenderEnhancedLine(state, pos2D);
                break;
        }
    }

    #region Rendering Methods

    private void RenderEnhancedLine(PlayerIndicatorState state, Vector2 targetPos)
    {
        var io = ImGui.GetIO();
        var centerScreen = io.DisplaySize * 0.5f;
        
        var lineStyle = _config.GetLineStyle();
        ApplyPlayerSpecificStyling(lineStyle, state);
        
        _graphicsRenderer.DrawEnhancedLine(centerScreen, targetPos, lineStyle);
    }

    private void RenderAnimatedOutline(PlayerIndicatorState state, Vector2 pos)
    {
        var outlineStyle = _config.GetOutlineStyle();
        ApplyPlayerSpecificStyling(outlineStyle, state);
        
        var radius = _config.ShapeSize;
        _graphicsRenderer.DrawEnhancedOutline(pos, radius, outlineStyle);
    }

    private void RenderGradientFill(PlayerIndicatorState state, Vector2 pos)
    {
        var gradientStyle = _config.GetGradientStyle();
        var radius = _config.ShapeSize;
        
        _graphicsRenderer.DrawGradientFill(pos, radius, gradientStyle);
    }

    private void RenderParticleEffect(PlayerIndicatorState state, Vector2 pos)
    {
        var particleStyle = _config.GetParticleStyle();
        ApplyPlayerSpecificStyling(particleStyle, state);
        
        _graphicsRenderer.DrawParticleEffect(pos, particleStyle);
    }

    private void RenderCustomShape(PlayerIndicatorState state, Vector2 pos)
    {
        var outlineStyle = _config.GetOutlineStyle();
        outlineStyle.Shape = _config.IndicatorShape;
        ApplyPlayerSpecificStyling(outlineStyle, state);
        
        var radius = _config.ShapeSize;
        _graphicsRenderer.DrawEnhancedOutline(pos, radius, outlineStyle);
    }

    private void RenderGlowEffect(PlayerIndicatorState state, Vector2 pos)
    {
        var outlineStyle = _config.GetOutlineStyle();
        outlineStyle.HasGlow = true;
        outlineStyle.GlowColor = _config.GlowColor;
        ApplyPlayerSpecificStyling(outlineStyle, state);
        
        var radius = _config.ShapeSize;
        _graphicsRenderer.DrawEnhancedOutline(pos, radius, outlineStyle);
    }

    private void RenderNeonStyle(PlayerIndicatorState state, Vector2 pos)
    {
        // Bright, vibrant neon effect
        var outlineStyle = _config.GetOutlineStyle();
        outlineStyle.HasGlow = true;
        outlineStyle.Color = AnimationSystem.GetCyclingColor(_config.PrimaryColor, 2f, 0.3f);
        outlineStyle.GlowColor = outlineStyle.Color;
        outlineStyle.AnimationType = AnimationType.Glow;
        
        var radius = _config.ShapeSize;
        _graphicsRenderer.DrawEnhancedOutline(pos, radius, outlineStyle);
    }

    private void RenderTacticalOverlay(PlayerIndicatorState state, Vector2 pos)
    {
        // Military-style square indicators
        var outlineStyle = _config.GetOutlineStyle();
        outlineStyle.Shape = IndicatorShape.Square;
        outlineStyle.Color = new Vector4(0f, 1f, 0f, 0.9f); // Green
        outlineStyle.AnimationType = AnimationType.None;
        
        var radius = _config.ShapeSize * 0.8f;
        _graphicsRenderer.DrawEnhancedOutline(pos, radius, outlineStyle);
    }

    private void RenderHolographicStyle(PlayerIndicatorState state, Vector2 pos)
    {
        // Futuristic holographic effect
        var outlineStyle = _config.GetOutlineStyle();
        outlineStyle.Color = new Vector4(0f, 0.8f, 1f, 0.7f);
        outlineStyle.HasGlow = true;
        outlineStyle.GlowColor = new Vector4(0.5f, 0.8f, 1f, 0.4f);
        outlineStyle.AnimationType = AnimationType.Shimmer;
        
        var radius = _config.ShapeSize;
        _graphicsRenderer.DrawEnhancedOutline(pos, radius, outlineStyle);
        
        // Add gradient fill for holographic effect
        var gradientStyle = new GradientStyle
        {
            Colors = new[] 
            { 
                new Vector4(0f, 0.8f, 1f, 0.3f), 
                new Vector4(0.5f, 0.8f, 1f, 0.1f),
                new Vector4(0f, 0.8f, 1f, 0f)
            },
            Direction = GradientDirection.Radial
        };
        _graphicsRenderer.DrawGradientFill(pos, radius * 0.8f, gradientStyle);
    }

    private void RenderMinimalistDot(PlayerIndicatorState state, Vector2 pos)
    {
        // Simple, clean dot
        var outlineStyle = _config.GetOutlineStyle();
        outlineStyle.Shape = IndicatorShape.Circle;
        outlineStyle.IsFilled = true;
        outlineStyle.Color = new Vector4(1f, 1f, 1f, 0.8f);
        outlineStyle.AnimationType = AnimationType.None;
        
        var radius = 3f;
        _graphicsRenderer.DrawEnhancedOutline(pos, radius, outlineStyle);
    }

    #endregion

    #region Helper Methods

    private void ApplyPlayerSpecificStyling(EnhancedLineStyle style, PlayerIndicatorState state)
    {
        // Apply player-specific modifications (health, status, etc.)
        if (state.IsLowHealth)
        {
            style.Color = new Vector4(1f, 0.5f, 0f, 1f); // Orange for low health
            style.AnimationType = AnimationType.Pulse;
        }
        
        if (state.IsVulnerable)
        {
            style.Color = new Vector4(1f, 1f, 0f, 1f); // Yellow for vulnerable
        }
    }

    private void ApplyPlayerSpecificStyling(EnhancedOutlineStyle style, PlayerIndicatorState state)
    {
        // Apply player-specific modifications
        if (state.IsLowHealth)
        {
            style.Color = new Vector4(1f, 0.5f, 0f, 1f);
            style.AnimationType = AnimationType.Pulse;
        }
        
        if (state.IsVulnerable)
        {
            style.Color = new Vector4(1f, 1f, 0f, 1f);
        }
    }

    private void ApplyPlayerSpecificStyling(ParticleEffectStyle style, PlayerIndicatorState state)
    {
        // Apply player-specific modifications
        if (state.IsLowHealth)
        {
            style.Color = new Vector4(1f, 0.5f, 0f, 1f);
            style.AnimationSpeed *= 1.5f;
        }
    }

    private Vector3? WorldToScreen(Vector3 worldPos)
    {
        // TODO: Implement proper world-to-screen conversion
        // This is a placeholder - you'll need to use the game's camera matrices
        // For now, return a dummy screen position
        var io = ImGui.GetIO();
        return new Vector3(
            io.DisplaySize.X * 0.5f + worldPos.X * 10f,
            io.DisplaySize.Y * 0.5f + worldPos.Z * 10f,
            0f
        );
    }

    private void CleanupOldStates()
    {
        var currentTime = AnimationSystem.CurrentTime;
        var keysToRemove = new List<ulong>();
        
        foreach (var kvp in _playerStates)
        {
            if (currentTime - kvp.Value.LastUpdateTime > 5f) // 5 seconds timeout
            {
                keysToRemove.Add(kvp.Key);
            }
        }
        
        foreach (var key in keysToRemove)
        {
            _playerStates.Remove(key);
        }
    }

    #endregion

    #region Rendering Logic

    /// <summary>
    /// Determine if enhanced graphics should be rendered based on configuration
    /// </summary>
    private bool ShouldRenderEnhancedGraphics()
    {
        // If enhanced graphics everywhere mode is enabled, always render (unless disabled)
        if (_config.EnhancedGraphicsEverywhereMode)
        {
            return true;
        }

        // If respecting original PvP setting, check if we're in PvP
        if (_config.RespectOriginalPvPSetting)
        {
            // TODO: Add PvP territory check here
            // For now, return true to allow rendering everywhere
            return true;
        }

        return true;
    }

    #endregion

    public void Dispose()
    {
        if (!_disposed)
        {
            _graphicsRenderer?.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// Tracks state for individual players
/// </summary>
public class PlayerIndicatorState
{
    public ulong PlayerId { get; }
    public float LastUpdateTime { get; private set; }
    public bool IsLowHealth { get; private set; }
    public bool IsVulnerable { get; private set; }
    public Vector3 LastPosition { get; private set; }
    public float DistanceFromPlayer { get; private set; }

    public PlayerIndicatorState(ulong playerId)
    {
        PlayerId = playerId;
        LastUpdateTime = AnimationSystem.CurrentTime;
    }

    public void Update(IPlayerCharacter player, Vector3 localPlayerPosition)
    {
        LastUpdateTime = AnimationSystem.CurrentTime;
        LastPosition = player.Position;
        DistanceFromPlayer = Vector3.Distance(player.Position, localPlayerPosition);
        
        // Update health status
        var healthPercent = (float)player.CurrentHp / player.MaxHp * 100f;
        IsLowHealth = healthPercent < 25f; // Low health threshold
        
        // TODO: Check for vulnerability status effects
        IsVulnerable = false; // Placeholder
    }
}
