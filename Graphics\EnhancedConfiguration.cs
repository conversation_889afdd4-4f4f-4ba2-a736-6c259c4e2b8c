using System;
using System.Numerics;
using Dalamud.Configuration;

namespace PvPLinePlugin.Graphics;

/// <summary>
/// Enhanced configuration for advanced graphics features
/// </summary>
[Serializable]
public class EnhancedConfiguration : IPluginConfiguration
{
    public int Version { get; set; } = 1;

    #region Enhanced Indicator Settings
    public EnhancedIndicatorType EnhancedIndicatorType { get; set; } = EnhancedIndicatorType.Lines;
    public bool UseEnhancedGraphics { get; set; } = false;
    
    // Animation Settings
    public AnimationType AnimationType { get; set; } = AnimationType.None;
    public float AnimationSpeed { get; set; } = 1.0f;
    public EasingType EasingType { get; set; } = EasingType.Linear;
    public EffectIntensity EffectIntensity { get; set; } = EffectIntensity.Moderate;
    
    // Visual Effects
    public bool EnableGlowEffects { get; set; } = false;
    public bool EnableParticleEffects { get; set; } = false;
    public bool EnableTrailEffects { get; set; } = false;
    public bool EnableGradientFills { get; set; } = false;
    
    // Line Style Settings
    public Vector4 PrimaryColor { get; set; } = new Vector4(1.0f, 0.0f, 0.0f, 1.0f);
    public Vector4 SecondaryColor { get; set; } = new Vector4(1.0f, 1.0f, 0.0f, 1.0f);
    public Vector4 GlowColor { get; set; } = new Vector4(1.0f, 0.5f, 0.0f, 0.8f);
    public float LineThickness { get; set; } = 2.0f;
    public float GlowIntensity { get; set; } = 0.5f;
    
    // Shape Settings
    public IndicatorShape IndicatorShape { get; set; } = IndicatorShape.Circle;
    public float ShapeSize { get; set; } = 20.0f;
    public bool FilledShapes { get; set; } = false;
    
    // Particle Settings
    public int ParticleCount { get; set; } = 8;
    public float ParticleSize { get; set; } = 2.0f;
    public float ParticleRadius { get; set; } = 25.0f;
    public float ParticleSpeed { get; set; } = 1.0f;
    
    // Gradient Settings
    public GradientDirection GradientDirection { get; set; } = GradientDirection.Radial;
    public Vector4[] GradientColors { get; set; } = 
    {
        new Vector4(1.0f, 0.0f, 0.0f, 1.0f),
        new Vector4(1.0f, 1.0f, 0.0f, 0.5f),
        new Vector4(1.0f, 0.0f, 0.0f, 0.0f)
    };
    
    // Advanced Animation Settings
    public float PulseAmplitude { get; set; } = 0.2f;
    public float RotationSpeed { get; set; } = 0.5f;
    public float WaveAmplitude { get; set; } = 5.0f;
    public float FadeAmount { get; set; } = 0.3f;
    
    // Performance Settings
    public bool EnableAntiAliasing { get; set; } = true;
    public bool EnableHardwareAcceleration { get; set; } = true;
    public int MaxParticles { get; set; } = 100;
    public float UpdateFrequency { get; set; } = 60.0f;
    
    // Preset Configurations
    public string CurrentPreset { get; set; } = "Default";
    #endregion

    #region Preset Management
    /// <summary>
    /// Apply a visual preset
    /// </summary>
    public void ApplyPreset(VisualPreset preset)
    {
        switch (preset)
        {
            case VisualPreset.Minimal:
                ApplyMinimalPreset();
                break;
            case VisualPreset.Standard:
                ApplyStandardPreset();
                break;
            case VisualPreset.Enhanced:
                ApplyEnhancedPreset();
                break;
            case VisualPreset.Neon:
                ApplyNeonPreset();
                break;
            case VisualPreset.Tactical:
                ApplyTacticalPreset();
                break;
            case VisualPreset.Holographic:
                ApplyHolographicPreset();
                break;
        }
    }

    private void ApplyMinimalPreset()
    {
        EnhancedIndicatorType = EnhancedIndicatorType.MinimalistDots;
        UseEnhancedGraphics = false;
        AnimationType = AnimationType.None;
        EnableGlowEffects = false;
        EnableParticleEffects = false;
        PrimaryColor = new Vector4(1.0f, 1.0f, 1.0f, 0.8f);
        LineThickness = 1.0f;
        CurrentPreset = "Minimal";
    }

    private void ApplyStandardPreset()
    {
        EnhancedIndicatorType = EnhancedIndicatorType.Lines;
        UseEnhancedGraphics = true;
        AnimationType = AnimationType.Pulse;
        AnimationSpeed = 1.0f;
        EnableGlowEffects = false;
        EnableParticleEffects = false;
        PrimaryColor = new Vector4(1.0f, 0.0f, 0.0f, 1.0f);
        LineThickness = 2.0f;
        CurrentPreset = "Standard";
    }

    private void ApplyEnhancedPreset()
    {
        EnhancedIndicatorType = EnhancedIndicatorType.AnimatedOutlines;
        UseEnhancedGraphics = true;
        AnimationType = AnimationType.Pulse;
        AnimationSpeed = 1.5f;
        EnableGlowEffects = true;
        EnableParticleEffects = false;
        PrimaryColor = new Vector4(1.0f, 0.2f, 0.0f, 1.0f);
        GlowColor = new Vector4(1.0f, 0.5f, 0.0f, 0.6f);
        LineThickness = 3.0f;
        GlowIntensity = 0.7f;
        CurrentPreset = "Enhanced";
    }

    private void ApplyNeonPreset()
    {
        EnhancedIndicatorType = EnhancedIndicatorType.NeonStyle;
        UseEnhancedGraphics = true;
        AnimationType = AnimationType.Glow;
        AnimationSpeed = 2.0f;
        EnableGlowEffects = true;
        EnableParticleEffects = true;
        PrimaryColor = new Vector4(0.0f, 1.0f, 1.0f, 1.0f);
        GlowColor = new Vector4(0.5f, 0.0f, 1.0f, 0.8f);
        LineThickness = 4.0f;
        GlowIntensity = 1.0f;
        ParticleCount = 12;
        CurrentPreset = "Neon";
    }

    private void ApplyTacticalPreset()
    {
        EnhancedIndicatorType = EnhancedIndicatorType.TacticalOverlay;
        UseEnhancedGraphics = true;
        AnimationType = AnimationType.None;
        EnableGlowEffects = false;
        EnableParticleEffects = false;
        PrimaryColor = new Vector4(0.0f, 1.0f, 0.0f, 0.9f);
        SecondaryColor = new Vector4(1.0f, 1.0f, 0.0f, 0.7f);
        LineThickness = 2.0f;
        IndicatorShape = IndicatorShape.Square;
        CurrentPreset = "Tactical";
    }

    private void ApplyHolographicPreset()
    {
        EnhancedIndicatorType = EnhancedIndicatorType.HolographicStyle;
        UseEnhancedGraphics = true;
        AnimationType = AnimationType.Shimmer;
        AnimationSpeed = 0.8f;
        EnableGlowEffects = true;
        EnableGradientFills = true;
        PrimaryColor = new Vector4(0.0f, 0.8f, 1.0f, 0.8f);
        GlowColor = new Vector4(0.5f, 0.8f, 1.0f, 0.4f);
        LineThickness = 3.0f;
        GradientDirection = GradientDirection.Radial;
        CurrentPreset = "Holographic";
    }
    #endregion

    /// <summary>
    /// Get line style based on current configuration
    /// </summary>
    public EnhancedLineStyle GetLineStyle()
    {
        return new EnhancedLineStyle
        {
            Color = PrimaryColor,
            Thickness = LineThickness,
            AnimationType = AnimationType,
            AnimationSpeed = AnimationSpeed,
            HasGlow = EnableGlowEffects,
            GlowColor = GlowColor,
            HasTrail = EnableTrailEffects,
            EasingType = EasingType
        };
    }

    /// <summary>
    /// Get outline style based on current configuration
    /// </summary>
    public EnhancedOutlineStyle GetOutlineStyle()
    {
        return new EnhancedOutlineStyle
        {
            Color = PrimaryColor,
            Thickness = LineThickness,
            AnimationType = AnimationType,
            AnimationSpeed = AnimationSpeed,
            Shape = IndicatorShape,
            IsFilled = FilledShapes,
            HasGlow = EnableGlowEffects,
            GlowColor = GlowColor,
            PulseAmplitude = PulseAmplitude,
            EasingType = EasingType
        };
    }

    /// <summary>
    /// Get particle effect style based on current configuration
    /// </summary>
    public ParticleEffectStyle GetParticleStyle()
    {
        return new ParticleEffectStyle
        {
            Color = SecondaryColor,
            ParticleCount = ParticleCount,
            Radius = ParticleRadius,
            ParticleSize = ParticleSize,
            AnimationSpeed = ParticleSpeed,
            RotationSpeed = RotationSpeed,
            WaveAmplitude = WaveAmplitude,
            FadeAmount = FadeAmount
        };
    }

    /// <summary>
    /// Get gradient style based on current configuration
    /// </summary>
    public GradientStyle GetGradientStyle()
    {
        return new GradientStyle
        {
            Colors = GradientColors,
            Direction = GradientDirection,
            AnimationSpeed = AnimationSpeed,
            IsAnimated = AnimationType != AnimationType.None
        };
    }
}

/// <summary>
/// Visual preset options
/// </summary>
public enum VisualPreset
{
    Minimal,
    Standard,
    Enhanced,
    Neon,
    Tactical,
    Holographic
}
