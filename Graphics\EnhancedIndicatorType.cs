using System;

namespace PvPLinePlugin.Graphics;

/// <summary>
/// Enhanced indicator types with advanced visual effects
/// </summary>
public enum EnhancedIndicatorType
{
    // Basic types (backwards compatible)
    Lines,
    Outlines,
    Nameplates,
    Icons,
    DirectionalArrows,
    HealthBars,
    Combination,
    
    // Enhanced types with advanced graphics
    AnimatedOutlines,      // Pulsing, rotating, flowing outlines
    GradientFills,         // Multi-color gradient fills
    ParticleEffects,       // Small animated particles
    CustomShapes,          // Triangles, diamonds, stars
    GlowEffects,          // Soft glowing halos
    TrailEffects,         // Motion trails
    RadarStyle,           // Sweeping radar lines
    HolographicStyle,     // Pseudo-3D with depth
    NeonStyle,            // Bright neon-like effects
    MinimalistDots,       // Clean, simple dot indicators
    GeometricShapes,      // Modern geometric indicators
    WaveEffects,          // Ripple/wave animations
    PulseRings,           // Expanding ring pulses
    LaserSight,           // Laser-like targeting lines
    TacticalOverlay       // Military-style tactical display
}

/// <summary>
/// Animation types for enhanced indicators
/// </summary>
public enum AnimationType
{
    None,
    Pulse,              // Size pulsing
    Rotate,             // Rotation animation
    Flow,               // Flowing/moving patterns
    Fade,               // Opacity fading
    Bounce,             // Bouncing motion
    Sweep,              // Sweeping motion
    Ripple,             // Ripple effect
    Glow,               // Glowing intensity
    Shimmer,            // Shimmering effect
    Spiral,             // Spiral motion
    Wave,               // Wave motion
    Flicker             // Flickering effect
}

/// <summary>
/// Easing functions for smooth animations
/// </summary>
public enum EasingType
{
    Linear,
    EaseIn,
    EaseOut,
    EaseInOut,
    Bounce,
    Elastic,
    Back,
    Sine,
    Quad,
    Cubic,
    Quart,
    Quint,
    Expo,
    Circ
}

/// <summary>
/// Visual effect intensity levels
/// </summary>
public enum EffectIntensity
{
    Subtle,
    Moderate,
    High,
    Extreme
}

/// <summary>
/// Gradient direction options
/// </summary>
public enum GradientDirection
{
    Horizontal,
    Vertical,
    Diagonal,
    Radial,
    Angular
}

/// <summary>
/// Shape types for custom indicators
/// </summary>
public enum IndicatorShape
{
    Circle,
    Square,
    Triangle,
    Diamond,
    Star,
    Hexagon,
    Cross,
    Arrow,
    Heart,
    Shield,
    Skull,
    Target,
    Plus,
    Minus,
    Custom
}
