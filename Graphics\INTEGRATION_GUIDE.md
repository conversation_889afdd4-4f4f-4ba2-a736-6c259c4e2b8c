# Enhanced Graphics Integration Guide

This guide shows you how to integrate the enhanced graphics system into your existing PvP Line Plugin.

## Step 1: Update Project Dependencies

The enhanced graphics system has been added to your project with these dependencies:
- ✅ SkiaSharp (3.119.0) - Advanced 2D graphics
- ✅ System.Numerics.Vectors (4.6.1) - Vector math and animations

## Step 2: Update Configuration Window

Your `ConfigWindow.cs` has been enhanced with:

### New Features Added:
- **Enhanced Mode Toggle**: Switch between classic and enhanced graphics
- **Enhanced Appearance Tab**: Advanced indicator type selection with live preview
- **Animation Tab**: Configure smooth animations and easing functions
- **Effects Tab**: Control glow, particles, gradients, and other visual effects
- **Presets Tab**: Quick visual presets for different styles

### Key Changes Made:
```csharp
// Added enhanced configuration support
private EnhancedConfiguration EnhancedConfig;
private bool UseEnhancedMode = false;

// Enhanced mode toggle in main UI
var enhancedMode = UseEnhancedMode;
if (ImGui.Checkbox("Enable Enhanced Graphics Mode", ref enhancedMode))
{
    UseEnhancedMode = enhancedMode;
    EnhancedConfig.UseEnhancedGraphics = enhancedMode;
}
```

## Step 3: Integration Options

You have several options for integrating the enhanced graphics:

### Option A: Full Integration (Recommended)
Replace your existing drawing system with the enhanced renderer:

```csharp
// In your main Plugin class
private EnhancedIndicatorRenderer? _enhancedRenderer;
private EnhancedConfiguration _enhancedConfig;

// Initialize in constructor
_enhancedConfig = new EnhancedConfiguration();
_enhancedRenderer = new EnhancedIndicatorRenderer(_enhancedConfig);
_enhancedRenderer.Initialize();

// In your draw method
if (_enhancedConfig.UseEnhancedGraphics)
{
    _enhancedRenderer.RenderIndicators(players, localPlayerPosition);
}
else
{
    // Keep existing classic rendering
    DrawClassicIndicators(players, localPlayerPosition);
}
```

### Option B: Side-by-Side (Safe)
Keep both systems and let users choose:

```csharp
// Use the PluginIntegrationExample class
private PluginIntegrationExample _graphicsIntegration;

// Initialize
_graphicsIntegration = new PluginIntegrationExample(Configuration);

// In draw method
_graphicsIntegration.DrawIndicators(players, localPlayerPosition);
```

### Option C: Gradual Migration
Start with enhanced UI only, add rendering later:

```csharp
// Just use the enhanced configuration window
// Keep existing rendering for now
// Users can configure enhanced settings but see classic rendering
```

## Step 4: Update Your Main Plugin File

Here's what you need to add to your main `Plugin.cs`:

```csharp
using PvPLinePlugin.Graphics;

public sealed class Plugin : IDalamudPlugin
{
    // Add enhanced graphics support
    private EnhancedConfiguration? _enhancedConfig;
    private EnhancedIndicatorRenderer? _enhancedRenderer;
    
    public Plugin(/* existing parameters */)
    {
        // Existing initialization...
        
        // Initialize enhanced graphics (optional)
        try
        {
            _enhancedConfig = new EnhancedConfiguration();
            _enhancedRenderer = new EnhancedIndicatorRenderer(_enhancedConfig);
            
            if (_enhancedRenderer.Initialize())
            {
                // Enhanced graphics available
            }
        }
        catch
        {
            // Fall back to classic rendering
            _enhancedRenderer = null;
        }
    }
    
    // In your existing draw method
    private void DrawIndicators()
    {
        if (_enhancedConfig?.UseEnhancedGraphics == true && _enhancedRenderer != null)
        {
            // Use enhanced rendering
            var (allies, enemies) = PvPOverlay.GetNearbyPlayers(Configuration);
            var allPlayers = allies.Concat(enemies);
            _enhancedRenderer.RenderIndicators(allPlayers, localPlayerPosition);
        }
        else
        {
            // Use existing classic rendering
            PvPOverlay.Draw(Configuration);
        }
    }
    
    public void Dispose()
    {
        // Existing cleanup...
        
        // Cleanup enhanced graphics
        _enhancedRenderer?.Dispose();
        EnhancedUI.Cleanup();
    }
}
```

## Step 5: Update Configuration Window Integration

Your `ConfigWindow.cs` already has the enhanced tabs. To fully integrate:

```csharp
public ConfigWindow(Plugin plugin) : base("PvP Line Plugin Configuration")
{
    // Existing initialization...
    
    // Initialize enhanced config
    EnhancedConfig = plugin.GetEnhancedConfiguration() ?? new EnhancedConfiguration();
}

// The enhanced tabs are already added in your DrawAppearanceTab method
```

## Step 6: Testing the Integration

1. **Build the project**: `dotnet build`
2. **Test classic mode**: Ensure existing functionality still works
3. **Test enhanced mode**: Enable enhanced graphics and try different presets
4. **Test performance**: Monitor FPS with different effect settings
5. **Test fallbacks**: Ensure graceful degradation if SkiaSharp fails

## Step 7: Performance Optimization

The enhanced graphics system includes automatic performance monitoring:

```csharp
// In your update loop
_graphicsIntegration?.MonitorPerformance();

// This automatically:
// - Reduces quality if FPS drops below 30
// - Increases quality if FPS is consistently above 60
// - Adjusts particle counts and effect intensity
```

## Available Visual Presets

Users can choose from these built-in presets:

- **Minimal**: Clean, simple indicators (good for low-end systems)
- **Standard**: Balanced visibility with subtle animations
- **Enhanced**: Rich visual effects with glow and animations
- **Neon**: Bright, vibrant neon-style effects
- **Tactical**: Military-style tactical display
- **Holographic**: Futuristic holographic appearance

## Troubleshooting

### Common Issues:

1. **SkiaSharp not loading**:
   - Ensure NuGet package is properly installed
   - Check that native libraries are included in output
   - Fall back to classic rendering

2. **Poor performance**:
   - Reduce particle counts
   - Disable complex effects
   - Lower update frequency
   - Use simpler indicator types

3. **Visual artifacts**:
   - Enable anti-aliasing
   - Check graphics driver updates
   - Verify SkiaSharp version compatibility

### Debug Mode:

Enable debug logging for troubleshooting:

```csharp
_enhancedConfig.EnableDebugLogging = true;
```

## Migration Path

For existing users:

1. **Automatic detection**: Enhanced mode is opt-in
2. **Settings migration**: Classic settings are preserved
3. **Gradual adoption**: Users can switch back and forth
4. **Performance safety**: Automatic quality adjustment

## Next Steps

After integration:

1. **Gather user feedback** on visual preferences
2. **Monitor performance** across different hardware
3. **Add custom presets** based on user requests
4. **Consider additional effects** like sound integration
5. **Optimize rendering** based on usage patterns

## Support

If you encounter issues:

1. Check the console for error messages
2. Try disabling enhanced graphics mode
3. Verify all dependencies are installed
4. Test with different visual presets
5. Monitor system performance

The enhanced graphics system is designed to be backward-compatible and performance-conscious, providing a smooth upgrade path for your existing plugin.
