using System;
using System.Numerics;
using SkiaSharp;
using ImGuiNET;

namespace PvPLinePlugin.Graphics;

/// <summary>
/// Interface for animated styles
/// </summary>
public interface IAnimatedStyle
{
    AnimationType AnimationType { get; }
    float AnimationSpeed { get; }
    Vector4 Color { get; }
}

/// <summary>
/// Style for enhanced lines
/// </summary>
public class EnhancedLineStyle : IAnimatedStyle
{
    public Vector4 Color { get; set; } = Vector4.One;
    public float Thickness { get; set; } = 2f;
    public AnimationType AnimationType { get; set; } = AnimationType.None;
    public float AnimationSpeed { get; set; } = 1f;
    public bool HasGlow { get; set; } = false;
    public Vector4 GlowColor { get; set; } = Vector4.One;
    public bool HasTrail { get; set; } = false;
    public EasingType EasingType { get; set; } = EasingType.Linear;
}

/// <summary>
/// Style for enhanced outlines
/// </summary>
public class EnhancedOutlineStyle : IAnimatedStyle
{
    public Vector4 Color { get; set; } = Vector4.One;
    public float Thickness { get; set; } = 2f;
    public AnimationType AnimationType { get; set; } = AnimationType.None;
    public float AnimationSpeed { get; set; } = 1f;
    public IndicatorShape Shape { get; set; } = IndicatorShape.Circle;
    public bool IsFilled { get; set; } = false;
    public bool HasGlow { get; set; } = false;
    public Vector4 GlowColor { get; set; } = Vector4.One;
    public float PulseAmplitude { get; set; } = 0.2f;
    public EasingType EasingType { get; set; } = EasingType.Linear;
}

/// <summary>
/// Style for particle effects
/// </summary>
public class ParticleEffectStyle
{
    public Vector4 Color { get; set; } = Vector4.One;
    public int ParticleCount { get; set; } = 8;
    public float Radius { get; set; } = 20f;
    public float ParticleSize { get; set; } = 2f;
    public float AnimationSpeed { get; set; } = 1f;
    public float RotationSpeed { get; set; } = 0.5f;
    public float WaveAmplitude { get; set; } = 5f;
    public float FadeAmount { get; set; } = 0.3f;
}

/// <summary>
/// Style for gradient effects
/// </summary>
public class GradientStyle
{
    public Vector4[] Colors { get; set; } = { Vector4.One, new Vector4(1, 1, 1, 0) };
    public GradientDirection Direction { get; set; } = GradientDirection.Radial;
    public float AnimationSpeed { get; set; } = 1f;
    public bool IsAnimated { get; set; } = false;
}

/// <summary>
/// Enhanced graphics renderer using SkiaSharp for advanced visual effects
/// </summary>
public class EnhancedGraphicsRenderer : IDisposable
{
    private SKSurface? _surface;
    private SKCanvas? _canvas;
    private bool _disposed = false;

    /// <summary>
    /// Initialize the graphics renderer
    /// </summary>
    public bool Initialize(int width, int height)
    {
        try
        {
            var info = new SKImageInfo(width, height, SKColorType.Rgba8888, SKAlphaType.Premul);
            _surface = SKSurface.Create(info);
            _canvas = _surface?.Canvas;
            return _canvas != null;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Begin drawing frame
    /// </summary>
    public void BeginFrame()
    {
        _canvas?.Clear(SKColors.Transparent);
    }

    /// <summary>
    /// End drawing frame and get the result
    /// </summary>
    public SKImage? EndFrame()
    {
        return _surface?.Snapshot();
    }

    /// <summary>
    /// Draw enhanced line with effects
    /// </summary>
    public void DrawEnhancedLine(Vector2 start, Vector2 end, EnhancedLineStyle style)
    {
        if (_canvas == null) return;

        using var paint = CreatePaint(style);
        
        // Apply animation effects
        if (style.AnimationType != AnimationType.None)
        {
            ApplyAnimation(paint, style);
        }

        // Draw main line
        _canvas.DrawLine(start.X, start.Y, end.X, end.Y, paint);

        // Draw additional effects
        if (style.HasGlow)
        {
            DrawGlowEffect(start, end, style);
        }

        if (style.HasTrail)
        {
            DrawTrailEffect(start, end, style);
        }
    }

    /// <summary>
    /// Draw enhanced outline around a point
    /// </summary>
    public void DrawEnhancedOutline(Vector2 center, float radius, EnhancedOutlineStyle style)
    {
        if (_canvas == null) return;

        using var paint = CreatePaint(style);
        
        // Apply animation
        if (style.AnimationType != AnimationType.None)
        {
            ApplyAnimation(paint, style);
            
            // Animate radius for pulse effects
            if (style.AnimationType == AnimationType.Pulse)
            {
                radius *= AnimationSystem.GetBreathingScale(1f, style.PulseAmplitude, style.AnimationSpeed);
            }
        }

        // Draw based on shape
        switch (style.Shape)
        {
            case IndicatorShape.Circle:
                _canvas.DrawCircle(center.X, center.Y, radius, paint);
                break;
            case IndicatorShape.Square:
                var rect = SKRect.Create(center.X - radius, center.Y - radius, radius * 2, radius * 2);
                _canvas.DrawRect(rect, paint);
                break;
            case IndicatorShape.Triangle:
                DrawTriangle(center, radius, paint);
                break;
            case IndicatorShape.Diamond:
                DrawDiamond(center, radius, paint);
                break;
            case IndicatorShape.Star:
                DrawStar(center, radius, paint);
                break;
            case IndicatorShape.Hexagon:
                DrawHexagon(center, radius, paint);
                break;
        }

        // Draw additional effects
        if (style.HasGlow)
        {
            DrawGlowOutline(center, radius, style);
        }
    }

    /// <summary>
    /// Draw particle effects
    /// </summary>
    public void DrawParticleEffect(Vector2 center, ParticleEffectStyle style)
    {
        if (_canvas == null) return;

        var time = AnimationSystem.CurrentTime;
        var particleCount = style.ParticleCount;
        
        for (int i = 0; i < particleCount; i++)
        {
            var angle = (float)(2 * Math.PI * i / particleCount);
            var distance = style.Radius + AnimationSystem.GetWaveValue(style.AnimationSpeed, style.WaveAmplitude, i * 0.5f);
            
            var particlePos = new Vector2(
                center.X + (float)Math.Cos(angle + time * style.RotationSpeed) * distance,
                center.Y + (float)Math.Sin(angle + time * style.RotationSpeed) * distance
            );

            using var paint = new SKPaint
            {
                Color = ToSKColor(style.Color),
                IsAntialias = true,
                Style = SKPaintStyle.Fill
            };

            // Animate particle opacity
            var opacity = AnimationSystem.GetFadeOpacity(style.Color.W, style.FadeAmount, style.AnimationSpeed + i * 0.1f);
            paint.Color = paint.Color.WithAlpha((byte)(opacity * 255));

            _canvas.DrawCircle(particlePos.X, particlePos.Y, style.ParticleSize, paint);
        }
    }

    /// <summary>
    /// Draw gradient fill
    /// </summary>
    public void DrawGradientFill(Vector2 center, float radius, GradientStyle style)
    {
        if (_canvas == null) return;

        using var shader = CreateGradientShader(center, radius, style);
        using var paint = new SKPaint
        {
            Shader = shader,
            IsAntialias = true,
            Style = SKPaintStyle.Fill
        };

        _canvas.DrawCircle(center.X, center.Y, radius, paint);
    }

    // Helper methods
    private SKPaint CreatePaint(EnhancedLineStyle style)
    {
        return new SKPaint
        {
            Color = ToSKColor(style.Color),
            StrokeWidth = style.Thickness,
            Style = SKPaintStyle.Stroke,
            IsAntialias = true,
            StrokeCap = SKStrokeCap.Round
        };
    }

    private SKPaint CreatePaint(EnhancedOutlineStyle style)
    {
        return new SKPaint
        {
            Color = ToSKColor(style.Color),
            StrokeWidth = style.Thickness,
            Style = style.IsFilled ? SKPaintStyle.Fill : SKPaintStyle.Stroke,
            IsAntialias = true
        };
    }

    private void ApplyAnimation(SKPaint paint, IAnimatedStyle style)
    {
        switch (style.AnimationType)
        {
            case AnimationType.Fade:
                var opacity = AnimationSystem.GetFadeOpacity(style.Color.W, 0.5f, style.AnimationSpeed);
                paint.Color = paint.Color.WithAlpha((byte)(opacity * 255));
                break;
            case AnimationType.Glow:
                var glowIntensity = AnimationSystem.GetPulseValue(style.AnimationSpeed, 0.5f);
                // Apply glow effect (simplified)
                paint.Color = paint.Color.WithAlpha((byte)((style.Color.W + glowIntensity) * 255));
                break;
        }
    }

    private void DrawGlowEffect(Vector2 start, Vector2 end, EnhancedLineStyle style)
    {
        if (_canvas == null) return;

        // Draw multiple lines with decreasing opacity for glow effect
        for (int i = 1; i <= 5; i++)
        {
            using var glowPaint = new SKPaint
            {
                Color = ToSKColor(style.GlowColor).WithAlpha((byte)(50 / i)),
                StrokeWidth = style.Thickness + i * 2,
                Style = SKPaintStyle.Stroke,
                IsAntialias = true,
                StrokeCap = SKStrokeCap.Round
            };
            
            _canvas.DrawLine(start.X, start.Y, end.X, end.Y, glowPaint);
        }
    }

    private void DrawTrailEffect(Vector2 start, Vector2 end, EnhancedLineStyle style)
    {
        // Implementation for trail effects would go here
        // This is a placeholder for the trail rendering logic
    }

    private void DrawGlowOutline(Vector2 center, float radius, EnhancedOutlineStyle style)
    {
        if (_canvas == null) return;

        // Similar to line glow but for outlines
        for (int i = 1; i <= 3; i++)
        {
            using var glowPaint = new SKPaint
            {
                Color = ToSKColor(style.GlowColor).WithAlpha((byte)(80 / i)),
                StrokeWidth = style.Thickness + i,
                Style = SKPaintStyle.Stroke,
                IsAntialias = true
            };
            
            _canvas.DrawCircle(center.X, center.Y, radius + i, glowPaint);
        }
    }

    private SKShader CreateGradientShader(Vector2 center, float radius, GradientStyle style)
    {
        var colors = new SKColor[style.Colors.Length];
        for (int i = 0; i < style.Colors.Length; i++)
        {
            colors[i] = ToSKColor(style.Colors[i]);
        }

        return style.Direction switch
        {
            GradientDirection.Radial => SKShader.CreateRadialGradient(
                new SKPoint(center.X, center.Y), radius, colors, null, SKShaderTileMode.Clamp),
            GradientDirection.Horizontal => SKShader.CreateLinearGradient(
                new SKPoint(center.X - radius, center.Y), new SKPoint(center.X + radius, center.Y), 
                colors, null, SKShaderTileMode.Clamp),
            GradientDirection.Vertical => SKShader.CreateLinearGradient(
                new SKPoint(center.X, center.Y - radius), new SKPoint(center.X, center.Y + radius), 
                colors, null, SKShaderTileMode.Clamp),
            _ => SKShader.CreateRadialGradient(
                new SKPoint(center.X, center.Y), radius, colors, null, SKShaderTileMode.Clamp)
        };
    }

    // Shape drawing methods
    private void DrawTriangle(Vector2 center, float radius, SKPaint paint)
    {
        var path = new SKPath();
        for (int i = 0; i < 3; i++)
        {
            var angle = (float)(i * 2 * Math.PI / 3 - Math.PI / 2);
            var x = center.X + (float)Math.Cos(angle) * radius;
            var y = center.Y + (float)Math.Sin(angle) * radius;
            
            if (i == 0) path.MoveTo(x, y);
            else path.LineTo(x, y);
        }
        path.Close();
        _canvas?.DrawPath(path, paint);
    }

    private void DrawDiamond(Vector2 center, float radius, SKPaint paint)
    {
        var path = new SKPath();
        path.MoveTo(center.X, center.Y - radius);
        path.LineTo(center.X + radius, center.Y);
        path.LineTo(center.X, center.Y + radius);
        path.LineTo(center.X - radius, center.Y);
        path.Close();
        _canvas?.DrawPath(path, paint);
    }

    private void DrawStar(Vector2 center, float radius, SKPaint paint)
    {
        var path = new SKPath();
        var outerRadius = radius;
        var innerRadius = radius * 0.4f;
        
        for (int i = 0; i < 10; i++)
        {
            var angle = (float)(i * Math.PI / 5 - Math.PI / 2);
            var r = (i % 2 == 0) ? outerRadius : innerRadius;
            var x = center.X + (float)Math.Cos(angle) * r;
            var y = center.Y + (float)Math.Sin(angle) * r;
            
            if (i == 0) path.MoveTo(x, y);
            else path.LineTo(x, y);
        }
        path.Close();
        _canvas?.DrawPath(path, paint);
    }

    private void DrawHexagon(Vector2 center, float radius, SKPaint paint)
    {
        var path = new SKPath();
        for (int i = 0; i < 6; i++)
        {
            var angle = (float)(i * Math.PI / 3);
            var x = center.X + (float)Math.Cos(angle) * radius;
            var y = center.Y + (float)Math.Sin(angle) * radius;
            
            if (i == 0) path.MoveTo(x, y);
            else path.LineTo(x, y);
        }
        path.Close();
        _canvas?.DrawPath(path, paint);
    }

    private static SKColor ToSKColor(Vector4 color)
    {
        return new SKColor(
            (byte)(color.X * 255),
            (byte)(color.Y * 255),
            (byte)(color.Z * 255),
            (byte)(color.W * 255)
        );
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _surface?.Dispose();
            _disposed = true;
        }
    }
}
