using System;
using System.Numerics;
using ImGuiNET;
using SkiaSharp;

namespace PvPLinePlugin.Graphics;

/// <summary>
/// Real-time preview renderer for the configuration window
/// </summary>
public class PreviewRenderer : IDisposable
{
    private readonly EnhancedGraphicsRenderer _graphicsRenderer;
    private SKBitmap? _previewBitmap;
    private IntPtr _textureId = IntPtr.Zero;
    private bool _disposed = false;
    private const int PreviewWidth = 300;
    private const int PreviewHeight = 200;

    public PreviewRenderer()
    {
        _graphicsRenderer = new EnhancedGraphicsRenderer();
        InitializePreview();
    }

    /// <summary>
    /// Initialize the preview system
    /// </summary>
    private bool InitializePreview()
    {
        try
        {
            _previewBitmap = new SKBitmap(PreviewWidth, PreviewHeight, SKColorType.Rgba8888, SKAlphaType.Premul);
            return _graphicsRenderer.Initialize(PreviewWidth, PreviewHeight);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Render a preview of the current configuration
    /// </summary>
    public void RenderPreview(EnhancedConfiguration config)
    {
        if (_previewBitmap == null) return;

        _graphicsRenderer.BeginFrame();

        // Create mock player positions for preview
        var center = new Vector2(PreviewWidth * 0.5f, PreviewHeight * 0.5f);
        var mockPositions = GenerateMockPlayerPositions(center);

        // Render indicators based on current configuration
        foreach (var pos in mockPositions)
        {
            RenderPreviewIndicator(pos, config);
        }

        var image = _graphicsRenderer.EndFrame();
        
        // Convert SkiaSharp image to bitmap for ImGui display
        if (image != null)
        {
            UpdatePreviewTexture(image);
        }
    }

    /// <summary>
    /// Display the preview in ImGui
    /// </summary>
    public void DisplayPreview()
    {
        if (_textureId != IntPtr.Zero)
        {
            ImGui.Text("Live Preview:");
            ImGui.Image(_textureId, new Vector2(PreviewWidth, PreviewHeight));
        }
        else
        {
            // Fallback to simple drawing if texture loading fails
            DisplayFallbackPreview();
        }
    }

    /// <summary>
    /// Display a simple fallback preview using ImGui drawing
    /// </summary>
    private void DisplayFallbackPreview()
    {
        var drawList = ImGui.GetWindowDrawList();
        var pos = ImGui.GetCursorScreenPos();
        var size = new Vector2(PreviewWidth, PreviewHeight);
        
        // Background
        drawList.AddRectFilled(pos, pos + size, ImGui.ColorConvertFloat4ToU32(new Vector4(0.1f, 0.1f, 0.1f, 1.0f)));
        drawList.AddRect(pos, pos + size, ImGui.ColorConvertFloat4ToU32(new Vector4(0.3f, 0.3f, 0.3f, 1.0f)));

        // Simple preview elements
        var center = pos + size * 0.5f;
        var mockPositions = GenerateMockPlayerPositions(center);

        foreach (var mockPos in mockPositions)
        {
            drawList.AddCircleFilled(mockPos, 5f, ImGui.ColorConvertFloat4ToU32(new Vector4(1f, 0f, 0f, 1f)));
        }

        ImGui.Dummy(size);
    }

    /// <summary>
    /// Render a single indicator in the preview
    /// </summary>
    private void RenderPreviewIndicator(Vector2 position, EnhancedConfiguration config)
    {
        switch (config.EnhancedIndicatorType)
        {
            case EnhancedIndicatorType.Lines:
                var center = new Vector2(PreviewWidth * 0.5f, PreviewHeight * 0.5f);
                _graphicsRenderer.DrawEnhancedLine(center, position, config.GetLineStyle());
                break;

            case EnhancedIndicatorType.AnimatedOutlines:
                _graphicsRenderer.DrawEnhancedOutline(position, config.ShapeSize * 0.5f, config.GetOutlineStyle());
                break;

            case EnhancedIndicatorType.GradientFills:
                _graphicsRenderer.DrawGradientFill(position, config.ShapeSize * 0.5f, config.GetGradientStyle());
                break;

            case EnhancedIndicatorType.ParticleEffects:
                _graphicsRenderer.DrawParticleEffect(position, config.GetParticleStyle());
                break;

            case EnhancedIndicatorType.CustomShapes:
                var outlineStyle = config.GetOutlineStyle();
                outlineStyle.Shape = config.IndicatorShape;
                _graphicsRenderer.DrawEnhancedOutline(position, config.ShapeSize * 0.5f, outlineStyle);
                break;

            case EnhancedIndicatorType.GlowEffects:
                var glowStyle = config.GetOutlineStyle();
                glowStyle.HasGlow = true;
                _graphicsRenderer.DrawEnhancedOutline(position, config.ShapeSize * 0.5f, glowStyle);
                break;

            case EnhancedIndicatorType.NeonStyle:
                var neonStyle = config.GetOutlineStyle();
                neonStyle.HasGlow = true;
                neonStyle.Color = AnimationSystem.GetCyclingColor(config.PrimaryColor, 2f, 0.3f);
                neonStyle.AnimationType = AnimationType.Glow;
                _graphicsRenderer.DrawEnhancedOutline(position, config.ShapeSize * 0.5f, neonStyle);
                break;

            default:
                _graphicsRenderer.DrawEnhancedOutline(position, config.ShapeSize * 0.5f, config.GetOutlineStyle());
                break;
        }
    }

    /// <summary>
    /// Generate mock player positions for preview
    /// </summary>
    private Vector2[] GenerateMockPlayerPositions(Vector2 center)
    {
        var positions = new Vector2[5];
        var radius = 60f;
        
        for (int i = 0; i < positions.Length; i++)
        {
            var angle = (float)(i * 2 * Math.PI / positions.Length);
            positions[i] = new Vector2(
                center.X + (float)Math.Cos(angle) * radius,
                center.Y + (float)Math.Sin(angle) * radius
            );
        }
        
        return positions;
    }

    /// <summary>
    /// Update the preview texture for ImGui display
    /// </summary>
    private void UpdatePreviewTexture(SKImage image)
    {
        try
        {
            // Convert SKImage to bitmap data
            using var pixmap = image.PeekPixels();
            if (pixmap != null && _previewBitmap != null)
            {
                pixmap.ReadPixels(_previewBitmap.Info, _previewBitmap.GetPixels(), _previewBitmap.RowBytes, 0, 0);
                
                // TODO: Upload bitmap to GPU texture for ImGui
                // This would require platform-specific texture creation
                // For now, we'll use the fallback preview
            }
        }
        catch
        {
            // Fall back to simple preview if texture update fails
        }
    }

    /// <summary>
    /// Create an animated preview that shows effects over time
    /// </summary>
    public void RenderAnimatedPreview(EnhancedConfiguration config)
    {
        if (!config.UseEnhancedGraphics) return;

        var drawList = ImGui.GetWindowDrawList();
        var pos = ImGui.GetCursorScreenPos();
        var size = new Vector2(PreviewWidth, PreviewHeight);
        
        // Background
        drawList.AddRectFilled(pos, pos + size, ImGui.ColorConvertFloat4ToU32(new Vector4(0.05f, 0.05f, 0.1f, 1.0f)));
        drawList.AddRect(pos, pos + size, ImGui.ColorConvertFloat4ToU32(new Vector4(0.2f, 0.2f, 0.3f, 1.0f)));

        var center = pos + size * 0.5f;
        
        // Animate based on current time
        var time = AnimationSystem.CurrentTime;
        
        // Show different preview elements based on indicator type
        switch (config.EnhancedIndicatorType)
        {
            case EnhancedIndicatorType.Lines:
                RenderLinePreview(drawList, center, config, time);
                break;
                
            case EnhancedIndicatorType.AnimatedOutlines:
                RenderOutlinePreview(drawList, center, config, time);
                break;
                
            case EnhancedIndicatorType.ParticleEffects:
                RenderParticlePreview(drawList, center, config, time);
                break;
                
            case EnhancedIndicatorType.NeonStyle:
                RenderNeonPreview(drawList, center, config, time);
                break;
                
            default:
                RenderGenericPreview(drawList, center, config, time);
                break;
        }

        ImGui.Dummy(size);
    }

    private void RenderLinePreview(ImDrawListPtr drawList, Vector2 center, EnhancedConfiguration config, float time)
    {
        var targetPositions = GenerateMockPlayerPositions(center);
        
        foreach (var target in targetPositions)
        {
            var color = config.PrimaryColor;
            var thickness = config.LineThickness;
            
            // Apply animation
            if (config.AnimationType == AnimationType.Pulse)
            {
                var pulse = AnimationSystem.GetPulseValue(config.AnimationSpeed, 0.3f);
                thickness *= (1f + pulse);
                color.W *= (1f + pulse * 0.5f);
            }
            
            drawList.AddLine(center, target, ImGui.ColorConvertFloat4ToU32(color), thickness);
        }
    }

    private void RenderOutlinePreview(ImDrawListPtr drawList, Vector2 center, EnhancedConfiguration config, float time)
    {
        var targetPositions = GenerateMockPlayerPositions(center);
        
        foreach (var target in targetPositions)
        {
            var color = config.PrimaryColor;
            var radius = config.ShapeSize * 0.3f;
            
            // Apply animation
            if (config.AnimationType == AnimationType.Pulse)
            {
                var pulse = AnimationSystem.GetPulseValue(config.AnimationSpeed, 0.2f);
                radius *= (1f + pulse);
            }
            
            drawList.AddCircle(target, radius, ImGui.ColorConvertFloat4ToU32(color), 16, config.LineThickness);
            
            // Add glow effect if enabled
            if (config.EnableGlowEffects)
            {
                var glowColor = config.GlowColor;
                glowColor.W *= 0.5f;
                drawList.AddCircle(target, radius + 2f, ImGui.ColorConvertFloat4ToU32(glowColor), 16, config.LineThickness + 1f);
            }
        }
    }

    private void RenderParticlePreview(ImDrawListPtr drawList, Vector2 center, EnhancedConfiguration config, float time)
    {
        var particleCount = Math.Min(config.ParticleCount, 12); // Limit for preview
        var radius = 40f;
        
        for (int i = 0; i < particleCount; i++)
        {
            var angle = (float)(i * 2 * Math.PI / particleCount + time * config.ParticleSpeed);
            var distance = radius + (float)Math.Sin(time * 2f + i) * 10f;
            
            var particlePos = new Vector2(
                center.X + (float)Math.Cos(angle) * distance,
                center.Y + (float)Math.Sin(angle) * distance
            );
            
            var color = config.SecondaryColor;
            color.W *= (0.5f + (float)Math.Sin(time * 3f + i) * 0.3f);
            
            drawList.AddCircleFilled(particlePos, config.ParticleSize, ImGui.ColorConvertFloat4ToU32(color));
        }
    }

    private void RenderNeonPreview(ImDrawListPtr drawList, Vector2 center, EnhancedConfiguration config, float time)
    {
        var targetPositions = GenerateMockPlayerPositions(center);
        
        foreach (var target in targetPositions)
        {
            // Cycling neon colors
            var hueShift = (float)Math.Sin(time * 2f) * 0.3f;
            var neonColor = new Vector4(
                Math.Clamp(0.2f + hueShift, 0f, 1f),
                Math.Clamp(0.8f + hueShift * 0.5f, 0f, 1f),
                Math.Clamp(1f + hueShift * 0.3f, 0f, 1f),
                1f
            );
            
            var radius = config.ShapeSize * 0.3f;
            var glowRadius = radius + 5f + (float)Math.Sin(time * 4f) * 3f;
            
            // Outer glow
            var glowColor = neonColor;
            glowColor.W = 0.3f;
            drawList.AddCircle(target, glowRadius, ImGui.ColorConvertFloat4ToU32(glowColor), 16, 3f);
            
            // Main circle
            drawList.AddCircle(target, radius, ImGui.ColorConvertFloat4ToU32(neonColor), 16, config.LineThickness);
        }
    }

    private void RenderGenericPreview(ImDrawListPtr drawList, Vector2 center, EnhancedConfiguration config, float time)
    {
        var targetPositions = GenerateMockPlayerPositions(center);
        
        foreach (var target in targetPositions)
        {
            var color = config.PrimaryColor;
            var radius = config.ShapeSize * 0.3f;
            
            drawList.AddCircle(target, radius, ImGui.ColorConvertFloat4ToU32(color), 16, config.LineThickness);
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _previewBitmap?.Dispose();
            _graphicsRenderer?.Dispose();
            
            // TODO: Clean up texture resources
            
            _disposed = true;
        }
    }
}
