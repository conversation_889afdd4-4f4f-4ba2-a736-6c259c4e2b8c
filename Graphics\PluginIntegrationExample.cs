using System;
using System.Collections.Generic;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;
using ImGuiNET;

namespace PvPLinePlugin.Graphics;

/// <summary>
/// Example of how to integrate the enhanced graphics system into the main plugin
/// This shows the modifications needed to support both classic and enhanced rendering
/// </summary>
public class PluginIntegrationExample
{
    private readonly Configuration _classicConfig;
    private readonly EnhancedConfiguration _enhancedConfig;
    private readonly EnhancedIndicatorRenderer? _enhancedRenderer;
    private bool _useEnhancedGraphics;

    public PluginIntegrationExample(Configuration classicConfig)
    {
        _classicConfig = classicConfig;
        _enhancedConfig = new EnhancedConfiguration();
        
        // Initialize enhanced renderer if SkiaSharp is available
        try
        {
            _enhancedRenderer = new EnhancedIndicatorRenderer(_enhancedConfig);
            if (_enhancedRenderer.Initialize())
            {
                _useEnhancedGraphics = true;
            }
        }
        catch (Exception)
        {
            // Fall back to classic rendering if enhanced graphics fail
            _useEnhancedGraphics = false;
            _enhancedRenderer?.Dispose();
        }
    }

    /// <summary>
    /// Main drawing method that chooses between classic and enhanced rendering
    /// </summary>
    public void DrawIndicators(IEnumerable<IPlayerCharacter> players, Vector3 localPlayerPosition)
    {
        if (_useEnhancedGraphics && _enhancedConfig.UseEnhancedGraphics && _enhancedRenderer != null)
        {
            // Enhanced graphics can work everywhere if configured to do so
            DrawEnhancedIndicators(players, localPlayerPosition);
        }
        else
        {
            // Classic rendering still respects the original PvP-only setting
            DrawClassicIndicators(players, localPlayerPosition);
        }
    }

    /// <summary>
    /// Enhanced graphics rendering path
    /// </summary>
    private void DrawEnhancedIndicators(IEnumerable<IPlayerCharacter> players, Vector3 localPlayerPosition)
    {
        // If enhanced graphics everywhere mode is enabled, get all nearby players
        if (_enhancedConfig.EnhancedGraphicsEverywhereMode)
        {
            var allNearbyPlayers = GetAllNearbyPlayers(localPlayerPosition);
            _enhancedRenderer?.RenderIndicators(allNearbyPlayers, localPlayerPosition);
        }
        else
        {
            // Use the provided players (which may be filtered by PvP zones)
            _enhancedRenderer?.RenderIndicators(players, localPlayerPosition);
        }
    }

    /// <summary>
    /// Classic ImGui rendering path (existing implementation)
    /// </summary>
    private void DrawClassicIndicators(IEnumerable<IPlayerCharacter> players, Vector3 localPlayerPosition)
    {
        var drawList = ImGui.GetBackgroundDrawList();
        var io = ImGui.GetIO();
        var centerScreen = io.DisplaySize * 0.5f;

        foreach (var player in players)
        {
            if (player?.Position == null) continue;

            var screenPos = WorldToScreen(player.Position);
            if (!screenPos.HasValue) continue;

            var targetPos = new Vector2(screenPos.Value.X, screenPos.Value.Y);
            var distance = Vector3.Distance(player.Position, localPlayerPosition);

            if (distance > _classicConfig.MaxDistance) continue;

            // Classic line drawing
            var color = GetPlayerColor(player);
            var thickness = GetLineThickness(player);

            drawList.AddLine(centerScreen, targetPos, color, thickness);

            // Add classic effects if enabled
            if (_classicConfig.PulseIndicators)
            {
                var pulseScale = 1f + (float)Math.Sin(DateTime.UtcNow.Ticks / 10000000.0 * 2) * 0.2f;
                drawList.AddLine(centerScreen, targetPos, color, thickness * pulseScale);
            }
        }
    }

    /// <summary>
    /// Configuration window integration example
    /// </summary>
    public void DrawConfigurationUI()
    {
        // Enhanced graphics toggle
        var enhancedMode = _useEnhancedGraphics && _enhancedConfig.UseEnhancedGraphics;
        if (ImGui.Checkbox("Enable Enhanced Graphics", ref enhancedMode))
        {
            _enhancedConfig.UseEnhancedGraphics = enhancedMode && _useEnhancedGraphics;
        }

        if (!_useEnhancedGraphics)
        {
            ImGui.SameLine();
            ImGui.TextColored(new Vector4(1f, 0.5f, 0f, 1f), "(SkiaSharp not available)");
        }

        ImGui.Separator();

        if (_enhancedConfig.UseEnhancedGraphics && _useEnhancedGraphics)
        {
            DrawEnhancedConfigurationTabs();
        }
        else
        {
            DrawClassicConfigurationTabs();
        }
    }

    /// <summary>
    /// Enhanced configuration tabs
    /// </summary>
    private void DrawEnhancedConfigurationTabs()
    {
        if (ImGui.BeginTabBar("EnhancedTabs"))
        {
            if (ImGui.BeginTabItem("Appearance"))
            {
                var currentType = _enhancedConfig.EnhancedIndicatorType;
                if (EnhancedUI.DrawIndicatorTypeSelector(ref currentType, _enhancedConfig))
                {
                    _enhancedConfig.EnhancedIndicatorType = currentType;
                }

                ImGui.EndTabItem();
            }

            if (ImGui.BeginTabItem("Animation"))
            {
                EnhancedUI.DrawAnimationControls(_enhancedConfig);
                ImGui.EndTabItem();
            }

            if (ImGui.BeginTabItem("Effects"))
            {
                EnhancedUI.DrawEffectsControls(_enhancedConfig);
                ImGui.EndTabItem();
            }

            if (ImGui.BeginTabItem("Presets"))
            {
                EnhancedUI.DrawPresetSelector(_enhancedConfig);
                ImGui.EndTabItem();
            }

            ImGui.EndTabBar();
        }
    }

    /// <summary>
    /// Classic configuration tabs (existing implementation)
    /// </summary>
    private void DrawClassicConfigurationTabs()
    {
        if (ImGui.BeginTabBar("ClassicTabs"))
        {
            if (ImGui.BeginTabItem("Appearance"))
            {
                // Classic appearance settings
                var lineColor = _classicConfig.LineColor;
                if (ImGui.ColorEdit4("Line Color", ref lineColor))
                {
                    _classicConfig.LineColor = lineColor;
                }

                var lineThickness = _classicConfig.LineThickness;
                if (ImGui.SliderFloat("Line Thickness", ref lineThickness, 1f, 10f))
                {
                    _classicConfig.LineThickness = lineThickness;
                }

                var pulseIndicators = _classicConfig.PulseIndicators;
                if (ImGui.Checkbox("Pulse Effect", ref pulseIndicators))
                {
                    _classicConfig.PulseIndicators = pulseIndicators;
                }

                ImGui.EndTabItem();
            }

            ImGui.EndTabBar();
        }
    }

    /// <summary>
    /// Migration helper to convert classic settings to enhanced settings
    /// </summary>
    public void MigrateClassicToEnhanced()
    {
        _enhancedConfig.PrimaryColor = _classicConfig.LineColor;
        _enhancedConfig.LineThickness = _classicConfig.LineThickness;
        _enhancedConfig.AnimationType = _classicConfig.PulseIndicators ? AnimationType.Pulse : AnimationType.None;
        
        // Set a reasonable enhanced indicator type based on classic settings
        _enhancedConfig.EnhancedIndicatorType = EnhancedIndicatorType.AnimatedOutlines;
        
        // Enable enhanced graphics
        _enhancedConfig.UseEnhancedGraphics = true;
    }

    /// <summary>
    /// Performance monitoring for enhanced graphics
    /// </summary>
    public void MonitorPerformance()
    {
        if (!_enhancedConfig.UseEnhancedGraphics) return;

        var io = ImGui.GetIO();
        var fps = io.Framerate;

        // Automatically adjust quality based on performance
        if (fps < 30f && _enhancedConfig.EffectIntensity > EffectIntensity.Subtle)
        {
            // Reduce quality to maintain performance
            _enhancedConfig.EffectIntensity = EffectIntensity.Subtle;
            _enhancedConfig.EnableParticleEffects = false;
            _enhancedConfig.ParticleCount = Math.Min(_enhancedConfig.ParticleCount, 8);
        }
        else if (fps > 60f && _enhancedConfig.EffectIntensity < EffectIntensity.High)
        {
            // Increase quality if performance allows
            _enhancedConfig.EffectIntensity = EffectIntensity.Moderate;
        }
    }

    /// <summary>
    /// Save/load enhanced configuration
    /// </summary>
    public void SaveEnhancedConfiguration()
    {
        // TODO: Implement saving enhanced configuration to file
        // This would extend the existing configuration system
    }

    public void LoadEnhancedConfiguration()
    {
        // TODO: Implement loading enhanced configuration from file
        // This would extend the existing configuration system
    }

    /// <summary>
    /// Get all nearby players regardless of PvP zone restrictions (for enhanced graphics)
    /// </summary>
    private IEnumerable<IPlayerCharacter> GetAllNearbyPlayers(Vector3 localPlayerPosition)
    {
        // Use the new PvPOverlay method that gets players everywhere
        var (allies, enemies) = PvPOverlay.GetNearbyPlayersEverywhere(_classicConfig);

        // Combine allies and enemies for enhanced graphics
        var allPlayers = new List<IPlayerCharacter>();
        allPlayers.AddRange(allies);
        allPlayers.AddRange(enemies);

        return allPlayers;
    }

    // Helper methods (these would be implemented in your actual plugin)
    private Vector3? WorldToScreen(Vector3 worldPos)
    {
        // Placeholder - implement actual world-to-screen conversion
        return worldPos;
    }

    private uint GetPlayerColor(IPlayerCharacter player)
    {
        // Placeholder - implement actual color logic
        return ImGui.ColorConvertFloat4ToU32(_classicConfig.LineColor);
    }

    private float GetLineThickness(IPlayerCharacter player)
    {
        // Placeholder - implement actual thickness logic
        return _classicConfig.LineThickness;
    }

    public void Dispose()
    {
        _enhancedRenderer?.Dispose();
    }
}

/// <summary>
/// Example of how to modify the main Plugin class
/// </summary>
public class PluginModificationExample
{
    /*
    // In your main Plugin class, you would add:
    
    private PluginIntegrationExample? _graphicsIntegration;
    
    // In the constructor:
    _graphicsIntegration = new PluginIntegrationExample(Configuration);
    
    // In your draw method:
    _graphicsIntegration?.DrawIndicators(players, localPlayerPosition);
    _graphicsIntegration?.MonitorPerformance();
    
    // In your config window:
    _graphicsIntegration?.DrawConfigurationUI();
    
    // In Dispose:
    _graphicsIntegration?.Dispose();
    */
}
